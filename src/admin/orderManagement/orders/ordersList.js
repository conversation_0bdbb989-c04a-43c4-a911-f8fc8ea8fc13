import React, { useCallback, useEffect, useMemo, useState } from "react";
import CommonAdminTable from "../../components/commonTable";
import { Button, Input, Modal, Pagination, Space, Tag, Typography } from "antd";
import { Link, useLocation } from "wouter";
import { EyeFilled, UnorderedListOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import Alertify from "../../../services/alertify";
import { OrdersListAPI } from "../../../redux/actions/adminActions/listsAction";
import { RefreshOrderItemsAPI } from "../../../redux/actions/adminActions/orderRefreshAction";
import { getQueryParams } from "../../../utilities/getQueryParams";
import { ReloadOutlined } from "@ant-design/icons";
import { useLoader } from "../../../utilities/useLoader";
import FullLoader from "../../../utilities/fullLoader";
import { OrdersErrorAPI } from "../../../redux/actions/adminActions/orderDetailsAction";
import { LoaderIcon } from "react-hot-toast";

const { Title } = Typography;

const OrdersList = () => {
  useEffect(() => {
    document.title = "Orders: ALP Shop";
  }, []);
  const [searchValue, setSearchValue] = useState("");
  const [totalOrders, setTotalOrders] = useState(0);
  const [, navigate] = useLocation();
  const params = getQueryParams();
  const initialPage = params.page || 1;
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [loader, startLoader, endLoader] = useLoader();
  const [fullLoader, startFullLoader, endFullLoader] = useLoader();

  const handlePageChange = (page) => {
    setCurrentPage(page);
    navigate(`/admin/orders?page=${page}`);
  };
  const dispatch = useDispatch();

  const fetchOrderRequests = useCallback(() => {
    const data = {
      search: searchValue,
      page: currentPage,
    };
    startFullLoader();
    dispatch(OrdersListAPI(data))
      .then((response) => {
        // console.log("List is-----:", response);
        const total = response?.payload?.data?.pagination?.total || 0;
        setTotalOrders(total);
        endFullLoader();
        // console.log("Total orders", total);
      })
      .catch((error) => {
        Alertify.error("Failed to fetch Order Request List: " + error.message);
        endFullLoader();
      });
  }, [dispatch, searchValue, currentPage]);

  useEffect(() => {
    const handler = setTimeout(() => {
      fetchOrderRequests();
    }, 100);

    return () => {
      clearTimeout(handler);
    };
  }, [fetchOrderRequests]);

  const orderRequests = useSelector((state) => state.all_lists);

  const all_orders = useMemo(() => {
    return orderRequests.all_orders_list || [];
  }, [orderRequests]);
  const [errorModal, setErrorModal] = useState(false);
  const [activeOrderId, setActiveOrderId] = useState("");
  const [errors, setErrors] = useState("");
  const [loader1, setLoader1] = useState(true);
  const columns = [
    {
      title: "Index",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (text, record, index) => (currentPage - 1) * 10 + index + 1,
    },
    {
      title: "Order Id",
      dataIndex: "orderId",
      key: "orderId",
      fixed: "left",
      render: (text) => <Link to="#">{text}</Link>,
    },
    {
      title: "User",
      dataIndex: "user",
      key: "user",
    },
    {
      title: "Order Total",
      dataIndex: "orderTotal",
      key: "orderTotal",
    },
    {
      title: "Order Type",
      dataIndex: "orderType",
      key: "orderType",
    },
    {
      title: "Requested Date",
      dataIndex: "requestedDate",
      key: "requestedDate",
    },
    {
      title: "Status",
      key: "status",
      dataIndex: "status",
      render: (status, record) => {
        let color =
          status === "Approved"
            ? "green"
            : status === "Error"
            ? "red"
            : "geekblue";
        return (
          <Tag
            color={color}
            key={status}
            className="font-bold"
            style={{ cursor: status === "Error" ? "pointer" : "default" }}
          >
            {status === "Error" ? (
              <p
                onClick={() => {
                  setErrorModal(true);
                  setActiveOrderId(record.orderId);
                  // console.log("Active record:", record.orderId);
                  const data = {
                    order_id: record.orderId,
                  };
                  setLoader1(true);
                  dispatch(OrdersErrorAPI(data))
                    .then((response) => {
                      setErrors(response.payload.data.data);
                      setLoader1(false);
                      // console.log("Errors--=-=-=-=-=-=-=-=-=-=-:", response);
                    })
                    .catch((error) => {
                      Alertify.error(
                        "Failed to get Orders' erros:",
                        error.message
                      );
                    });
                  // console.log("Errors--=-=-=-=-=-=-=-=-=-=-:", errors);
                }}
              >
                FAILED
              </p>
            ) : (
              status.toUpperCase()
            )}
          </Tag>
        );
      },
    },
    {
      title: "Action",
      key: "action",
      align: "left",
      render: (record) => (
        <Space size="middle">
          {record.orderType === "Normal Order" ? (
            <Link
              to={`/admin/orders/order-detail?orderId=${record.orderId}`}
              className="text-red-600"
            >
              <EyeFilled className="me-2" />
              View
            </Link>
          ) : record.status !== "Failed" ? ( // Hide Sub Orders link if status is "Failed"
            <>
              <div className="flex flex-col gap-2">
                <Link
                  to={`/admin/orders/order-detail?orderId=${record.orderId}`}
                  className="text-red-600"
                >
                  <EyeFilled className="me-2" />
                  View
                </Link>
                {record.status !== "Error" && (
                  <div>
                    <Link
                      to={`/admin/orders/sub-order-list?orderId=${record.orderId}`}
                      className="text-blue-700"
                    >
                      <UnorderedListOutlined className="me-2" />
                      Sub Orders
                    </Link>
                  </div>
                )}
              </div>
            </>
          ) : (
            <Link
              to={`/admin/orders/order-detail?orderId=${record.orderId}`}
              className="text-red-600"
            >
              <EyeFilled className="me-2" />
              View
            </Link>
          )}
        </Space>
      ),
    },
  ];

  // Add a unique key to each order request
  const dataSource = all_orders.map((order, index) => ({
    ...order,
    key: order.orderId || index,
  }));
  const handleRefresh = () => {
    startLoader();
    dispatch(RefreshOrderItemsAPI())
      .unwrap()
      .then((response) => {
        // console.log(response?.data, "this is the response");
        if (response?.data.status === "success" && response.status === 200) {
          endLoader();
          fetchOrderRequests();
          Alertify.success(response?.data?.message);
        }
      })
      .catch((error) => {
        endLoader();
        Alertify.error(
          "Failed to fetch Order Status: " + (error?.message || "Unknown error")
        );
      });
  };

  return (
    <>
      <Title level={2} className="text-left pb-4">
        Orders
      </Title>
      <div className="mb-8 flex items-center justify-between max-sm:flex-col max-sm:gap-4">
        <div className="sm:w-1/3">
          <Input
            placeholder="Search"
            value={searchValue}
            onChange={(e) => {
              setSearchValue(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
        <div className="sm:w-1/3 flex justify-end">
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            className="flex items-center gap-2"
            loading={loader}
          >
            REFRESH
          </Button>
        </div>
      </div>
      {fullLoader ? (
        <FullLoader isLoading={true} />
      ) : (
        <>
          <CommonAdminTable columns={columns} data={dataSource} />
          <div className="flex justify-between items-center mt-4">
            <Pagination
              current={currentPage}
              total={totalOrders}
              pageSize={10}
              onChange={handlePageChange}
              showSizeChanger={false}
            />
          </div>
        </>
      )}
      <Modal
        open={errorModal}
        onCancel={() => setErrorModal(false)}
        footer={false}
        centered
        className="rounded-lg border-2"
      >
        <div className="flex items-center gap-16">
          <h1 className="mb-0 font-bold text-[2rem]">{activeOrderId}</h1>
          <span className="text-[#CD4444] font-medium bg-[#FFEBEB] px-4 py-1 rounded-md">
            Failed
          </span>
        </div>
        <div className="bg-[#F3F8FC] p-4 rounded-md">
          {!loader1 ? (
            <>
              {errors.map((item, index) => (
                <div key={index} className="border-b-2 p-2 border-gray-400">
                  <p className="font-semibold">Error Message {index + 1}</p>
                  <p>{item.Error_Message}</p>
                </div>
              ))}
            </>
          ) : (
            <LoaderIcon />
          )}
        </div>
      </Modal>
    </>
  );
};

export default OrdersList;
