import { HomeOutlined, ProfileOutlined, ShopOutlined } from "@ant-design/icons";
import { Button, Typography } from "antd";
import { useState } from "react";
import { Link } from "wouter";
import ProfileModal from "../components/profileModal";

const { Title } = Typography;
const MyAccount = () => {
  const { isProfileModal, setIsProfileModal } = useState(false);
  return (
    <div className="bg-gray-50 rounded-lg m-4 p-4 shadow-lg sm:my-16 sm:pb-32">
      <Title level={2}>My Account</Title>
      <div className="flex items-center justify-center gap-5 flex-wrap">
        <Button
          className="max-sm:w-full h-full sm:px-32 py-8 text-2xl border border-[#0474B8] rounded-lg text-[#0474B8] flex gap-2 items-center justify-center font-bold"
          //   onClick={() => setIsProfileModal(true)}
          onClick={() => setIsProfileModal(true)}
        >
          <ProfileOutlined /> My Profile
        </Button>
        <Link
          to="/"
          className="max-sm:w-full sm:px-32 py-8 text-2xl border border-[#0474B8] rounded-lg text-[#0474B8] flex gap-2 items-center justify-center font-bold"
        >
          <ShopOutlined /> My Orders
        </Link>
        <Link
          to="/"
          className="max-sm:w-full sm:px-32 py-8 text-2xl border border-[#0474B8] rounded-lg text-[#0474B8] flex gap-2 items-center justify-center font-bold"
        >
          <HomeOutlined /> My Addresses
        </Link>
      </div>
      {isProfileModal && (
        <ProfileModal
          isProfileModal={isProfileModal}
          setIsProfileModal={setIsProfileModal}
        />
      )}
    </div>
  );
};

export default MyAccount;
